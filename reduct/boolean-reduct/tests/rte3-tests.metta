! (register-module! ../../../../metta-moses)

! (import! &self metta-moses:utilities:general-helpers)
! (import! &self metta-moses:reduct:boolean-reduct:rte-helpers)
! (import! &self metta-moses:reduct:boolean-reduct:cut-unnecessary-or)
! (import! &self metta-moses:reduct:boolean-reduct:cut-unnecessary-and)
! (import! &self metta-moses:reduct:boolean-reduct:delete-inconsistent-handle)
! (import! &self metta-moses:reduct:boolean-reduct:zero-constraint-subsumption)
! (import! &self metta-moses:reduct:boolean-reduct:one-constraint-subsumption)
! (import! &self metta-moses:reduct:boolean-reduct:promote-common-constraints)
! (import! &self metta-moses:reduct:boolean-reduct:reduce-to-elegance)

;; Test cases from python implementation.

!(assertEqual (reduceToElegance (AND (NOT A)) (NOT A) () ()) ((AND (NOT A)) (NOT A) False))
;
;; (AND (OR (NOT A) (NOT B))) ==> (AND (OR (NOT A ) (NOT B)))
!(assertEqual (reduceToElegance (AND (OR (NOT A) (NOT B))) (OR (NOT A) (NOT B)) () ()) ((AND (OR (NOT A) (NOT B))) (OR (NOT A) (NOT B)) False))
;; (AND (NOT A) (NOT B)) ==> (AND (NOT A) (NOT B))
!(assertEqual (reduceToElegance (AND (AND (NOT A) (NOT B))) (AND (NOT A) (NOT B)) () ()) ((AND (AND (NOT A) (NOT B))) (AND (NOT A) (NOT B)) False))
;; (AND A B) ==> (AND A B)
!(assertEqual (reduceToElegance (AND (AND A B)) (AND A B) () ()) ((AND (AND A B)) (AND A B) False))
;; (AND (OR A B)) ==> (AND (OR A B))
!(assertEqual (reduceToElegance (AND (OR A B)) (OR A B) () ()) ((AND (OR A B)) (OR A B) False))
;; (AND (OR (AND (NOT A) (NOT B)) (NOT C) (NOT D))) ==> (AND (OR (AND (NOT A) (NOT B)) (NOT C) (NOT D)))
!(assertEqual (reduceToElegance (AND (OR (AND (NOT A) (NOT B)) (NOT C) (NOT D))) (OR (AND (NOT A) (NOT B)) (NOT C) (NOT D)) () ()) ((AND (NOT A) (NOT B)) () True))
;; (AND (A A)) ==> (AND (A A))
!(assertEqual (reduceToElegance (AND (AND (A A))) (AND (A A)) () ()) ((AND A) () False))
;; (AND A A C) ==> (AND A A C)
!(assertEqual (reduceToElegance (AND (AND A A C)) (AND A A C) () ()) ((AND (AND A A C)) (AND A A C) False))
;; (AND A B (OR C D)) ==> (AND A B (OR C D))
!(assertEqual (reduceToElegance (AND (AND A B (OR C D))) (AND A B (OR C D)) () ()) ((AND (AND A B (OR C D))) (AND A B (OR C D)) False))
;; (AND A B C D C) ==> (AND A B C D C)
!(assertEqual (reduceToElegance (AND (AND A B C D C)) (AND A B C D C) () ()) ((AND (AND A B C D C)) (AND A B C D C) False))
;; (AND A (OR B (AND C (OR D E A)))) ==> (AND A (OR B C))
!(assertEqual (reduceToElegance (AND (AND A (OR (AND B) (AND C (OR (AND D) (AND E) (AND A)))))) (AND A (OR (AND B) (AND C (OR (AND D) (AND E) (AND A))))) () ()) ((AND (AND A (OR (AND B) (AND C)))) (AND A (OR (AND B) (AND C))) False))
;; (AND (NOT A) (NOT C) B (OR B (NOT A))) ==> (AND (NOT A) (NOT C) B)
!(assertEqual (reduceToElegance (AND (AND (NOT A) (NOT C) B (OR B (NOT A)))) (AND (NOT A) (NOT C) B (OR B (NOT A))) () ()) ((AND (AND (NOT A) (NOT C) B (OR B (NOT A)))) (AND (NOT A) (NOT C) B (OR B (NOT A))) False))
;; (AND A (OR B (AND C (OR D (AND E (NOT A)))))) ==> (AND A (OR B (AND C D)))
!(assertEqual (reduceToElegance (AND (AND A (OR B (AND C (OR D (AND E (NOT A))))))) (AND A (OR B (AND C (OR D (AND E (NOT A)))))) () ()) ((AND) () False))
;; (AND A B A A A) ==> (AND A B A A A)
!(assertEqual (reduceToElegance (AND (AND A B A A A)) (AND A B A A A) () ()) ((AND (AND A B A A A)) (AND A B A A A) False))


