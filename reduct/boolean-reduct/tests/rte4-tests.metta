! (register-module! ../../../../metta-moses)

! (import! &self metta-moses:utilities:general-helpers)
! (import! &self metta-moses:reduct:boolean-reduct:rte-helpers)
! (import! &self metta-moses:reduct:boolean-reduct:cut-unnecessary-or)
! (import! &self metta-moses:reduct:boolean-reduct:cut-unnecessary-and)
! (import! &self metta-moses:reduct:boolean-reduct:delete-inconsistent-handle)
! (import! &self metta-moses:reduct:boolean-reduct:zero-constraint-subsumption)
! (import! &self metta-moses:reduct:boolean-reduct:one-constraint-subsumption)
! (import! &self metta-moses:reduct:boolean-reduct:promote-common-constraints)
! (import! &self metta-moses:reduct:boolean-reduct:reduce-to-elegance)

;; (AND A B C D (NOT B)) ==> (AND)
!(assertEqual (reduceToElegance (AND (AND A B C D (NOT B))) (AND A B C D (NOT B)) () ()) ((AND) () False))
;; (AND B (NOT C) (NOT A) (OR B (NOT A))) ==> (AND B (NOT C) (NOT A))
!(assertEqual (reduceToElegance (AND (AND B (NOT C) (NOT A) (OR B (NOT A)))) (AND B (NOT C) (NOT A) (OR B (NOT A))) () ()) ((AND (AND B (NOT C) (NOT A) (OR B (NOT A)))) (AND B (NOT C) (NOT A) (OR B (NOT A))) False))
;; (AND B (NOT C) (NOT A) (OR B (AND (NOT K) (NOT X)))) ==> (AND B (NOT C) (NOT A))
!(assertEqual (reduceToElegance (AND (AND B (NOT C) (NOT A) (OR (AND B) (AND (NOT K) (NOT X))))) (AND B (NOT C) (NOT A) (OR (AND B) (AND (NOT K) (NOT X)))) () ()) ((AND (AND B (NOT C) (NOT A))) (AND B (NOT C) (NOT A)) False))
;; (AND (NOT A) (NOT C) B (OR (NOT B) (NOT A))) ==> (AND (NOT A) (NOT C) B)
!(assertEqual (reduceToElegance (AND (AND (NOT A) (NOT C) B (OR (AND (NOT B)) (AND (NOT A))))) (AND (NOT A) (NOT C) B (OR (AND (NOT B)) (AND (NOT A)))) () ()) ((AND (AND (NOT A) (NOT C) B)) (AND (NOT A) (NOT C) B) False))
;; (AND B (NOT C) (NOT A) (OR (NOT B) (NOT C) (NOT D) (NOT A))) ==> (AND B (NOT C) (NOT A))
!(assertEqual (reduceToElegance (AND (AND B (NOT C) (NOT A) (OR (NOT B) (NOT C) (NOT D) (NOT A)))) (AND B (NOT C) (NOT A) (OR (NOT B) (NOT C) (NOT D) (NOT A))) () ()) ((AND (AND B (NOT C) (NOT A) (OR (NOT B) (NOT C) (NOT D) (NOT A)))) (AND B (NOT C) (NOT A) (OR (NOT B) (NOT C) (NOT D) (NOT A))) False))
;; (AND A B C B A A (NOT A) (OR A B C A)) ==> (AND)
!(assertEqual (reduceToElegance (AND (AND A B C B A A (NOT A) (OR A B C A))) (AND A B C B A A (NOT A) (OR A B C A)) () ()) ((AND) () False))
;; (AND (OR A B)) ==> (AND (OR A B))
!(assertEqual (reduceToElegance (AND (OR A B)) (OR A B) () ()) ((AND (OR A B)) (OR A B) False))
;; (AND (OR A A)) ==> (AND A)
!(assertEqual (reduceToElegance (AND (OR A A)) (OR A A) () ()) ((AND (OR A A)) (OR A A) False)) ;; ((AND A) () False)
;; (AND (OR (AND A B) A C)) ==> (AND (OR (AND A B) A C))
!(assertEqual (reduceToElegance (AND (OR (AND A B) A C)) (OR (AND A B) A C) () ()) ((AND A B) () True))
;; (AND (OR (AND A B) (AND A B))) ==> (AND A B)
!(assertEqual (reduceToElegance (AND (OR (AND A B) (AND A B))) (OR (AND A B) (AND A B)) () ()) ((AND A B) () True))
;; (AND (OR (AND A B) C D)) ==> (AND (OR (AND A B) C D))
!(assertEqual (reduceToElegance (AND (OR (AND A B) C D)) (OR (AND A B) C D) () ()) ((AND A B) () True))
;; (AND (OR (NOT A) (NOT C) (NOT D))) ==> (AND (OR (NOT A) (NOT C) (NOT D)))
!(assertEqual (reduceToElegance (AND (OR (NOT A) (NOT C) (NOT D))) (OR (NOT A) (NOT C) (NOT D)) () ()) ((AND (OR (NOT A) (NOT C) (NOT D))) (OR (NOT A) (NOT C) (NOT D)) False))
;; (AND (OR (AND A (OR (NOT B) (NOT C))) D)) ==> (AND (OR (AND A (OR (NOT B) (NOT C))) D))
!(assertEqual (reduceToElegance (AND (OR (AND A (OR (NOT B) (NOT C))) D)) (OR (AND A (OR (NOT B) (NOT C))) D) () ()) ((AND A (OR D (NOT B) (NOT C))) (OR D (NOT B) (NOT C)) True))
;; (AND (OR A B C D C)) ==> (AND (OR A B C D C))
!(assertEqual (reduceToElegance (AND (OR A B C D C)) (OR A B C D C) () ()) ((AND (OR A B C D C)) (OR A B C D C) False))
;; (AND (OR A (AND B C D C))) ==> (AND (OR A (AND B C D C)))
!(assertEqual (reduceToElegance (AND (OR A (AND B C D C))) (OR A (AND B C D C)) () ()) ((AND B C D) () True))
;; (AND (OR (NOT C) (NOT D) (AND (NOT A) B))) ==> (AND (OR (NOT C) (NOT D) (AND (NOT A) B)))
!(assertEqual (reduceToElegance (AND (OR (NOT C) (NOT D) (AND (NOT A) B))) (OR (NOT C) (NOT D) (AND (NOT A) B)) () ()) ((AND (NOT A) B) () True))
;; (AND (OR A B C D (NOT C))) ==> (AND (OR A B C D (NOT C)))
!(assertEqual (reduceToElegance (AND (OR A B C D (NOT C))) (OR A B C D (NOT C)) () ()) ((AND (OR A B C D (NOT C))) (OR A B C D (NOT C)) False))
;; (AND (OR (AND A B) (AND A C) (AND A D))) ==> (AND A (OR B C D))
!(assertEqual (reduceToElegance (AND (OR (AND A B) (AND A C) (AND A D))) (OR (AND A B) (AND A C) (AND A D)) () ()) ((AND A (OR (AND B) (AND C) (AND D))) (OR (AND B) (AND C) (AND D)) True))
;; (AND (OR (AND A B) (AND A C (OR D (AND E A))))) ==> (AND A (OR B (AND C (OR D E))))
!(assertEqual (reduceToElegance (AND (OR (AND A B) (AND A C (OR (AND D) (AND E A))))) (OR (AND A B) (AND A C (OR (AND D) (AND E A)))) () ()) ((AND A (OR (AND B) (AND C (OR (AND D) (AND E A))))) (OR (AND B) (AND C (OR (AND D) (AND E A)))) True))  ;; INFO: This test case fails bec(reduceToElegance (AND (OR (AND A B) (AND A C (OR (AND D) (AND E) (AND A))))) (OR (AND A B) (AND A C (OR (AND D) (AND E) (AND A)))) () ())uase RTE needs to be called again if the parent chang ed
!(assertEqual (reduceToElegance (AND (AND A (OR (AND B) (AND C (OR (AND D) (AND E A)))))) (AND A (OR (AND B) (AND C (OR (AND D) (AND E A))))) () ()) ((AND (AND A (OR (AND B) (AND C (OR (AND D) (AND E)))))) (AND A (OR (AND B) (AND C (OR (AND D) (AND E))))) False))


