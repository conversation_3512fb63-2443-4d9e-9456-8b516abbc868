;; Generate all possible combination of neighbors at a given distance.
;; Given a center instance, which is a list of numbers, we generate all
;;  variations of that instance at a specified distance.
;;
;; For example for a center instance of (0 0 0 1), a possible neighbor
;;  at distance 1 would be (0 0 0 0) or (0 0 1 1). The distance used is
;;  a hamming distance of bits.
;;
;; This specific function is a wrapper for the varyNKnobs function. It
;;  cleans the output of varyNknobs to be unqiue and remove the center
;;  instance occurrence from the result. It also handles type conversion for compatiblity.
;;
;; Params:
;;    (mkMultip $m) - The multiplicity of the knobs
;;    (mkInst (Cons $x $xs)) - The center instance. It doesn't handle Nil case because
;;                              it doesn't make sense to have an empty instance.
;;    $dist - The hamming distance from the center instance to search for neighbors
;;
;; Returns: List of instances.
(: generateAllInNeighborhood (-> Multiplicity Instance Number (List Instance)))
(= (generateAllInNeighborhood (mkMultip $m) (mkInst (Cons $x $xs)) $dist)
   (if (== $dist 0)
       (Cons (mkInst (Cons $x $xs)) Nil)
       (let $multips (genList 1 (- $m 1))
          (let $instExp (List.listToExpr (Cons $x $xs))
          (let $listNeighbors (collapse (let $neighbors (varyNKnobs $multips $instExp $dist)
                                      (mkInst (pyExprToList $neighbors))))
          (let $uqListNeighbors (unique-atom $listNeighbors)
          (let $uqListNeighbors' (subtraction-atom $uqListNeighbors ((mkInst (Cons $x $xs))))
            (pyExprToList $uqListNeighbors'))))))))


;; This function returns a non deterministic list of instances at the given distance.
;; It's output isn't typed and also needs further preprocessing.
;; The function is never meant to be called on it's own but used in the generateAllInNeighborhood
;;  which will do the proper post processing.
;;
;; It takes the initial center instance and generates neighbors.
;; It then uses those generated neighbors as the next center
;;  instances to continue searching. Also it removes most of
;;  the center instances generated by it, it never removes the
;;  original center instance it was called with.
;;
;; Params:
;;   $mutlips: The possible bit values for each number in the instance. It is generated using the multiplicity of the knob. For example, for a discrete knob with 3 values, the $multips would be (1 2). The value 0 is encoded in the code it self so no need to include it in the list.
;;   $instExp: The center instance as an expression of numbers
;;   $dist: The hamming distance
;;
;; Returns: A non deterministic list of instances as an expression of numbers.
(: varyNKnobs (-> Expression Expression Number Expression))
(= (varyNKnobs $multips $instExp $dist)
    (if (== $dist 0)
        $instExp
        (let $cndInst (collapse (vary1Knobs $multips $instExp))
        (let $uqCndInst (unique-atom $cndInst)
        (let $neighbors (let
                            $nextCenter
                            (superpose $uqCndInst)
                            (varyNKnobs $multips $nextCenter (- $dist 1))
                        )
        (subtraction-atom $neighbors ($instExp)))))))

;; A helper function for the varyNKnobs function. This function simply generates all possible neighbors from a center instance at distance 1. It may include duplicate instances but that will be processed by the varyNKnobs function.
;; $multips: The possible bit values for each number in the instance.
;; $instExp: The center instance as an expression of numbers.
;;
;; Returns: A non deterministic list of instances as an expression of numbers.
(: vary1Knobs (-> Expression Expression Expression))
(= (vary1Knobs $multips ()) (empty))
(= (vary1Knobs $multips $instExp)
   (let $i (superpose $multips)
   (let ($h $t) (decons-atom $instExp)
     (superpose (
                  (let $a (if (== $i $h) 0 $i) (cons-atom $a $t))
                  (let $b (vary1Knobs $multips $t) (cons-atom $h $b)))))))

;; This function generates $samplesize instances that are within a distance (difference in knob specifications) 
;; of $dist  from the central instance. It uses the knobmapper to extract the multiplicity of the knob,
;; as the central instance only contains the list of specifications.
;; Parameters:
;;      $samplesize: The number of instances to be generated.
;;      $dist: The distance from the central instance.
;;      $knobmapper: The knob mapper used to extract the multiplicity of the knob.
;;      $centralinstance: The instance from which other instances will be generated.
(: sampleFromNeighborhood` (-> Number Number KnobMap Instance Instance))
(= (sampleFromNeighborhood` $n $dist (mkKbMap (mkDscKbMp $itDiscKnob) (mkDscMp $disc)) (mkInst $centralInst))
        (let* 
            (
              ($knobLen (MultiMap.length $disc)) 
              ($instLen (List.length $centralInst))
              ) 
              (if (== $knobLen $instLen) 
                  (let* (
                    ($selectedIndices (lazyRandomSelector 0 (- $instLen 1) $dist))
                    ($updatedInst (foldl-atom $selectedIndices $centralInst $updated $rand (updateInst $rand $disc $updated)))
                    ($inst (mkInst $updatedInst))
                  )
                  $inst) 
                  (Error ($knobLen $instLen) "Not Equal"))))

;; This is a helper function for sfn (sampling from neighborhood) that updates the 
;; central instance.
;; 
;; Parameters:
;;      $rand: A random numbers generated by lazyRandomSelector. Used to upadte the newInst
;;      $disc: A multi-map (extracted from the knob mapper) of DiscSpec logicalSubtreeKnob, 
;;             used to retrieve the lsk (logical subtree knob) from which the knob multiplicity 
;;             can be obtained.
;;      $newInst: The original central instance.
(: updatedInst (-> Number (MultiMap (DiscSpec LogicalSubtreeKnob)) Instance Instance))
(= (updateInst $rand $disc $newInst)
        (let*
          (
            (($decSpec (mkLSK (mkDiscKnob $knob (mkMultip $multi) $spec $spec1 $specList) $tree)) (MultiMap.getByIdx $rand $disc))
            ($curSpec (List.getByIdx $newInst $rand))
            ($upper (- $multi 1))
            ($randomSpec (+ (random-int &rng 0 $upper) 1))
            ($updatedInst (if (== $randomSpec $curSpec)
                              (List.replaceAt $newInst $rand 0)
                              (List.replaceAt $newInst $rand $randomSpec))))
          $updatedInst))

;; This function is Pre and post-processor function for sampleFromNeighborhood` 
;; to achive not-deterministic approach. It generates a list with the size of $sampleSize 
;; and calls sampleFromNeighborhood` function for every number in the list. 
;; This will make the sampleFromNeighborhood` to produce $sampleSize samples. 
;; Then the produced expression will be made in to llist using pyExprToList
(: sampleFromNeighborhood (-> Number Number KnobMap Instance (List Instance)))
(= (sampleFromNeighborhood $sampleSize $dist $kbMp $inst)
   (let* (
      (() (println! (Inside sampleFromNeighborhood: $sampleSize $dist $inst)))
      ($list (genList (- $sampleSize 1)))
      ($x (map-atom $list $i (sampleFromNeighborhood` $i $dist $kbMp $inst)))
    )
    (pyExprToList $x)))

;; Computes the hamming distance between two instances.
;; Example: inst1: (0 0 0 1), inst2: (0 0 0 0) => distance: 1
;;          inst1: (0 1 0 1), inst2: (0 0 0 0) => distance: 2
(: distance (-> Expression Expression Number) )
(= (distance $inst1 $inst2)
   (if (~= (size-atom $inst1) (size-atom $inst2))
       (Error (Inst: $inst1 and Inst: $inst2) (Instances are not equal length))
   (if (== $inst1 ())
       0
       (let ($h1 $t1) (decons-atom $inst1)
       (let ($h2 $t2) (decons-atom $inst2)
          (+ (if (== $h1 $h2) 0 1) (distance $t1 $t2)))))))

(: distance (-> Instance Instance Number) )
(= (distance (mkInst Nil) (mkInst Nil)) 0)
(= (distance (mkInst (Cons $x $xs)) (mkInst (Cons $y $ys)))
      (+ (if (== $x $y) 0 1) (distance (mkInst $xs) (mkInst $ys))))

;; A trick function to use to prevent the type error
;;  bugs that may rise as a result of overloaded types
;;  of the distance function
(: distance' (-> Instance Instance Number))
(= (distance' $inst1 $inst2) (distance $inst1 $inst2))

;; Assigns an initial large negative score to an instance.
;; Params:
;;   $inst: The instance to be scored.
;; Returns:
;;   (ScoredInstance $score): Scored instance with an initial large negative score -10^37.
(: initInstScore (-> Instance (ScoredInstance $score)))
(= (initInstScore $inst)
(mkSInst (mkPair $inst (worstCscore))))

;; converts a Scored Instance back to a normal Instance (used to the purpose of testing the sampleNewInstances method)
;; Param: scored instance
;; Returns:a normal instance without any score
(: removeInstScore (ScoredInstance $score) Instance)
(= (removeInstScore (mkSInst (mkPair $inst $score))) $inst)

;; Samples or generates new instances at a specified distance from a center instance,
;; assigns random scores, and updates the Deme by appending scored instances.
;; Params:
;;   $totalNeighbors: Estimated number of neighbors at distance $dist.
;;   $numNewInstances: Desired number of new instances to add.
;;   $centerInst: Reference instance for distance calculations.
;;   $deme: Deme
;;   $dist: Distance from $centerInst for sampling/generating instances.
;; Returns:
;;   (Deme Number) - Tuple of updated Deme with new scored instances appended and number of new instances added.
(: sampleNewInstances (-> Number Number Instance Deme Number (Deme Number)))
(= (sampleNewInstances $totalNeighbors $numNewInstances $centerInst (mkDeme (mkRep $kbMp $tree) (mkSInstSet $instList) $demeId) $dist)
 (let*
  (
    (() (println! (Inside sampleNewInstances: $totalNeighbors $numNewInstances $centerInst)))
    (() (println! (Distance: $dist CenterInst: $centerInst)))
    (() (println! ""))

    ($updatedTotalNeighbors
         (if (> (* 2 $numNewInstances) $totalNeighbors)
             (countNeighborhood $kbMp $centerInst $dist $numNewInstances)
             $totalNeighbors))
    ($updatedNumNewInstances 
         (if (< $numNewInstances $updatedTotalNeighbors)  
             $numNewInstances
             $updatedTotalNeighbors))
    ($newInstances (if (< $numNewInstances $updatedTotalNeighbors)
                       (sampleFromNeighborhood $numNewInstances $dist $kbMp $centerInst)
                       (generateAllInNeighborhood (mkMultip 3) $centerInst $dist)))
    ($scoredNewInstances (List.map initInstScore $newInstances))
    ($updatedInstList (List.concat $instList $scoredNewInstances))
 )
 ((mkDeme (mkRep $kbMp $tree) (mkSInstSet $updatedInstList) $demeId) $updatedNumNewInstances)))
